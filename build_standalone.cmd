@echo off
echo Building GangHaiCity Standalone...

REM Build the project
call prebuild.cmd
cd code
call ..\vendor\premake\bin\release\premake5.exe vs2022 --game=five
cd ..

REM Build using MSBuild
"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" code\build\five\CitizenMP.sln /p:Configuration=Release /p:Platform=x64

REM Create standalone directory
if exist "GangHaiCity_Standalone" rmdir /s /q "GangHaiCity_Standalone"
mkdir "GangHaiCity_Standalone"

REM Copy main executable
copy "code\build\five\bin\release\GangHaiCity.exe" "GangHaiCity_Standalone\"

REM Copy essential DLLs only (minimal set)
copy "code\build\five\bin\release\msvcp140.dll" "GangHaiCity_Standalone\" 2>nul
copy "code\build\five\bin\release\vcruntime140.dll" "GangHaiCity_Standalone\" 2>nul
copy "code\build\five\bin\release\vcruntime140_1.dll" "GangHaiCity_Standalone\" 2>nul

echo.
echo Standalone build complete!
echo You can now copy the GangHaiCity_Standalone folder to desktop
echo The exe will run independently without FiveM dependencies
echo.
pause
