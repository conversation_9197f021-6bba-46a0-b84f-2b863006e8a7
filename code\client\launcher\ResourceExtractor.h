#pragma once

#include <windows.h>
#include <string>
#include <vector>

class ResourceExtractor
{
public:
    static bool ExtractEmbeddedResources();
    static bool ExtractResourceToFile(int resourceId, const std::wstring& fileName);
    static std::wstring GetTempDirectory();
    static bool CreateDirectoryRecursive(const std::wstring& path);

private:
    static bool ExtractResource(HMODULE hModule, int resourceId, const std::wstring& outputPath);
};

// Resource IDs for embedded files
#define IDR_CORERT_DLL      1001
#define IDR_CITIZENGAME_DLL 1002
#define IDR_MSVCP140_DLL    1003
#define IDR_VCRUNTIME140_DLL 1004
