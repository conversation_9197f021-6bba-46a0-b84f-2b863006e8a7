@echo off
echo Post-build: Creating standalone GangHaiCity.exe...

set BUILD_DIR=%1
set CONFIG=%2

if "%BUILD_DIR%"=="" (
    echo Error: Build directory not specified
    exit /b 1
)

if "%CONFIG%"=="" (
    set CONFIG=Release
)

echo Build Directory: %BUILD_DIR%
echo Configuration: %CONFIG%

REM Create standalone directory
set STANDALONE_DIR=%BUILD_DIR%\GangHaiCity_Standalone
if exist "%STANDALONE_DIR%" rmdir /s /q "%STANDALONE_DIR%"
mkdir "%STANDALONE_DIR%"

REM Copy main executable
if exist "%BUILD_DIR%\GangHaiCity.exe" (
    copy "%BUILD_DIR%\GangHaiCity.exe" "%STANDALONE_DIR%\"
    echo Copied GangHaiCity.exe
) else (
    echo Warning: GangHaiCity.exe not found in %BUILD_DIR%
)

REM Copy essential DLLs if they exist
if exist "%BUILD_DIR%\CoreRT.dll" (
    copy "%BUILD_DIR%\CoreRT.dll" "%STANDALONE_DIR%\"
    echo Copied CoreRT.dll
)

if exist "%BUILD_DIR%\CitizenGame.dll" (
    copy "%BUILD_DIR%\CitizenGame.dll" "%STANDALONE_DIR%\"
    echo Copied CitizenGame.dll
)

REM Copy Visual C++ Runtime DLLs
for %%f in (msvcp140.dll vcruntime140.dll vcruntime140_1.dll) do (
    if exist "%BUILD_DIR%\%%f" (
        copy "%BUILD_DIR%\%%f" "%STANDALONE_DIR%\"
        echo Copied %%f
    )
)

echo.
echo Standalone package created in: %STANDALONE_DIR%
echo You can copy this folder to desktop and run GangHaiCity.exe independently!
echo.
