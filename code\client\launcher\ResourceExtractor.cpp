#include "StdInc.h"
#include "ResourceExtractor.h"
#include <shlobj.h>
#include <filesystem>

bool ResourceExtractor::ExtractEmbeddedResources()
{
    std::wstring tempDir = GetTempDirectory();
    if (tempDir.empty())
        return false;

    // Create GangHaiCity temp directory
    std::wstring gangHaiDir = tempDir + L"\\GangHaiCity";
    if (!CreateDirectoryRecursive(gangHaiDir))
        return false;

    // Extract essential DLLs
    bool success = true;
    success &= ExtractResourceToFile(IDR_CORERT_DLL, gangHaiDir + L"\\CoreRT.dll");
    success &= ExtractResourceToFile(IDR_CITIZENGAME_DLL, gangHaiDir + L"\\CitizenGame.dll");
    success &= ExtractResourceToFile(IDR_MSVCP140_DLL, gangHaiDir + L"\\msvcp140.dll");
    success &= ExtractResourceToFile(IDR_VCRUNTIME140_DLL, gangHaiDir + L"\\vcruntime140.dll");

    if (success)
    {
        // Set current directory to temp directory
        SetCurrentDirectoryW(gangHaiDir.c_str());
        
        // Add to PATH
        std::wstring currentPath;
        DWORD pathLen = GetEnvironmentVariableW(L"PATH", nullptr, 0);
        if (pathLen > 0)
        {
            currentPath.resize(pathLen);
            GetEnvironmentVariableW(L"PATH", &currentPath[0], pathLen);
        }
        
        std::wstring newPath = gangHaiDir + L";" + currentPath;
        SetEnvironmentVariableW(L"PATH", newPath.c_str());
    }

    return success;
}

bool ResourceExtractor::ExtractResourceToFile(int resourceId, const std::wstring& fileName)
{
    HMODULE hModule = GetModuleHandle(nullptr);
    return ExtractResource(hModule, resourceId, fileName);
}

std::wstring ResourceExtractor::GetTempDirectory()
{
    wchar_t tempPath[MAX_PATH];
    DWORD result = GetTempPathW(MAX_PATH, tempPath);
    if (result == 0 || result > MAX_PATH)
        return L"";
    
    return std::wstring(tempPath);
}

bool ResourceExtractor::CreateDirectoryRecursive(const std::wstring& path)
{
    try
    {
        std::filesystem::create_directories(path);
        return true;
    }
    catch (...)
    {
        return false;
    }
}

bool ResourceExtractor::ExtractResource(HMODULE hModule, int resourceId, const std::wstring& outputPath)
{
    HRSRC hResource = FindResourceW(hModule, MAKEINTRESOURCEW(resourceId), RT_RCDATA);
    if (!hResource)
        return false;

    HGLOBAL hLoadedResource = LoadResource(hModule, hResource);
    if (!hLoadedResource)
        return false;

    LPVOID pLockedResource = LockResource(hLoadedResource);
    if (!pLockedResource)
        return false;

    DWORD dwResourceSize = SizeofResource(hModule, hResource);
    if (dwResourceSize == 0)
        return false;

    HANDLE hFile = CreateFileW(outputPath.c_str(), GENERIC_WRITE, 0, nullptr, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, nullptr);
    if (hFile == INVALID_HANDLE_VALUE)
        return false;

    DWORD dwBytesWritten;
    BOOL bResult = WriteFile(hFile, pLockedResource, dwResourceSize, &dwBytesWritten, nullptr);
    CloseHandle(hFile);

    return bResult && (dwBytesWritten == dwResourceSize);
}
